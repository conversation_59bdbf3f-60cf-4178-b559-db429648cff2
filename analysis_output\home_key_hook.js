
// 首页键事件处理Hook脚本
// Copyright (c) 2025 By.举个🌰

Java.perform(function() {
    console.log("[*] 开始Hook首页键处理流程...");
    
    // Hook InputManagerService
    var InputManagerService = Java.use("com.android.server.input.InputManagerService");
    if (InputManagerService) {
        InputManagerService.notifyKey.implementation = function(deviceId, source, policyFlags, action, flags, keyCode, scanCode, metaState, downTime, eventTime) {
            if (keyCode == 3) { // KEYCODE_HOME
                console.log("[HOME_KEY] 检测到首页键事件");
                console.log("  时间: " + new Date().toISOString());
                console.log("  动作: " + (action == 0 ? "按下" : "抬起"));
                console.log("  设备ID: " + deviceId);
            }
            return this.notifyKey(deviceId, source, policyFlags, action, flags, keyCode, scanCode, metaState, downTime, eventTime);
        };
    }
    
    // Hook ActivityManagerService
    var ActivityManagerService = Java.use("com.android.server.am.ActivityManagerService");
    if (ActivityManagerService) {
        ActivityManagerService.startHomeActivityLocked.implementation = function(userId, reason) {
            console.log("[HOME_ACTIVITY] 启动首页Activity");
            console.log("  时间: " + new Date().toISOString());
            console.log("  用户ID: " + userId);
            console.log("  原因: " + reason);
            var start_time = Date.now();
            var result = this.startHomeActivityLocked(userId, reason);
            var end_time = Date.now();
            console.log("  耗时: " + (end_time - start_time) + "ms");
            return result;
        };
    }
});

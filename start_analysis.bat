@echo off
chcp 65001 >nul
title 晶晨机顶盒按首页键性能分析工具

echo.
echo ========================================
echo 晶晨机顶盒按首页键性能分析工具
echo 版权所有 © 2025 By.举个🌰
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查当前目录是否包含分析工具
if not exist "homekey_performance_analyzer.py" (
    echo ❌ 未找到分析工具文件，请确保在正确的目录中运行此脚本
    pause
    exit /b 1
)

REM 显示菜单
:menu
echo.
echo 请选择分析模式:
echo 1. 完整分析 (推荐)
echo 2. 仅日志分析
echo 3. 仅生成Frida脚本
echo 4. 自定义IP地址
echo 5. 退出
echo.
set /p choice=请输入选择 (1-5): 

if "%choice%"=="1" goto complete_analysis
if "%choice%"=="2" goto logcat_analysis
if "%choice%"=="3" goto frida_scripts
if "%choice%"=="4" goto custom_ip
if "%choice%"=="5" goto exit
echo 无效选择，请重新输入
goto menu

:complete_analysis
echo.
echo 🚀 启动完整分析模式...
python run_analysis.py --complete
goto end

:logcat_analysis
echo.
set /p duration=请输入监控时长(秒，默认30): 
if "%duration%"=="" set duration=30
echo 🚀 启动日志分析模式，监控时长: %duration%秒...
python run_analysis.py --logcat-only --duration %duration%
goto end

:frida_scripts
echo.
echo 🚀 生成Frida Hook脚本...
python run_analysis.py --frida-only
goto end

:custom_ip
echo.
set /p device_ip=请输入设备IP地址 (默认: ***************): 
if "%device_ip%"=="" set device_ip=***************
echo 🚀 使用设备IP: %device_ip%
python run_analysis.py --ip %device_ip% --complete
goto end

:end
echo.
echo ========================================
echo 分析完成！
echo 📁 请查看 analysis_output 目录中的结果文件
echo ========================================
pause
goto menu

:exit
echo.
echo 👋 感谢使用！
pause
exit /b 0

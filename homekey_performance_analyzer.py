#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
晶晨机顶盒按首页键响应缓慢问题分析工具
用于分析Android 9系统机顶盒的首页键响应延迟问题

Copyright (c) 2025 By.举个🌰
All rights reserved.
"""

import subprocess
import threading
import time
import os
import json
import re
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import argparse


class HomeKeyPerformanceAnalyzer:
    """晶晨机顶盒按首页键性能分析器"""
    
    def __init__(self, device_ip: str = "***************", adb_port: int = 5555):
        self.device_ip = device_ip
        self.adb_port = adb_port
        self.device_id = f"{device_ip}:{adb_port}"
        self.log_file = f"homekey_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        self.analysis_results = {}
        
        # 创建输出目录
        self.output_dir = "analysis_output"
        os.makedirs(self.output_dir, exist_ok=True)
        
        self.log_message("=" * 60)
        self.log_message("晶晨机顶盒按首页键性能分析工具启动")
        self.log_message(f"设备IP: {device_ip}")
        self.log_message(f"ADB端口: {adb_port}")
        self.log_message("版权所有 © 2025 By.举个🌰")
        self.log_message("=" * 60)

    def log_message(self, message: str, level: str = "INFO"):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}"
        print(log_entry)
        
        # 写入日志文件
        log_path = os.path.join(self.output_dir, self.log_file)
        with open(log_path, "a", encoding="utf-8") as f:
            f.write(log_entry + "\n")

    def run_adb_command(self, command: str, timeout: int = 30) -> Tuple[bool, str, str]:
        """执行ADB命令"""
        full_command = f"adb -s {self.device_id} {command}"
        self.log_message(f"执行命令: {full_command}")
        
        try:
            result = subprocess.run(
                full_command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=timeout,
                encoding='utf-8',
                errors='ignore'
            )
            
            success = result.returncode == 0
            if not success:
                self.log_message(f"命令执行失败: {result.stderr}", "ERROR")
            
            return success, result.stdout, result.stderr
            
        except subprocess.TimeoutExpired:
            self.log_message(f"命令执行超时: {command}", "ERROR")
            return False, "", "命令执行超时"
        except Exception as e:
            self.log_message(f"命令执行异常: {str(e)}", "ERROR")
            return False, "", str(e)

    def check_adb_connection(self) -> bool:
        """检查ADB连接状态"""
        self.log_message("检查ADB连接状态...")
        
        # 首先尝试连接设备
        success, stdout, stderr = self.run_adb_command("connect", timeout=10)
        if not success:
            self.log_message("ADB连接失败", "ERROR")
            return False
        
        # 检查设备是否在线
        success, stdout, stderr = self.run_adb_command("shell echo 'connected'", timeout=10)
        if success and "connected" in stdout:
            self.log_message("ADB连接正常")
            return True
        else:
            self.log_message("设备未响应", "ERROR")
            return False

    def get_device_info(self) -> Dict[str, str]:
        """获取设备信息"""
        self.log_message("获取设备信息...")
        device_info = {}
        
        info_commands = {
            "android_version": "shell getprop ro.build.version.release",
            "sdk_version": "shell getprop ro.build.version.sdk",
            "device_model": "shell getprop ro.product.model",
            "manufacturer": "shell getprop ro.product.manufacturer",
            "cpu_abi": "shell getprop ro.product.cpu.abi",
            "launcher_package": "shell cmd package resolve-activity --brief android.intent.action.MAIN android.intent.category.HOME",
            "memory_info": "shell cat /proc/meminfo | head -5"
        }
        
        for key, command in info_commands.items():
            success, stdout, stderr = self.run_adb_command(command)
            if success:
                device_info[key] = stdout.strip()
            else:
                device_info[key] = "获取失败"
        
        # 记录设备信息
        self.log_message("设备信息:")
        for key, value in device_info.items():
            self.log_message(f"  {key}: {value}")
        
        return device_info

    def start_logcat_monitoring(self, duration: int = 30) -> str:
        """开始logcat监控"""
        self.log_message(f"开始logcat监控，持续时间: {duration}秒")
        
        # 清除现有日志
        self.run_adb_command("logcat -c")
        
        # 创建日志文件
        logcat_file = os.path.join(self.output_dir, f"logcat_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        
        # 启动logcat监控
        logcat_command = f"adb -s {self.device_id} logcat -v threadtime"
        
        def monitor_logcat():
            try:
                with open(logcat_file, "w", encoding="utf-8") as f:
                    process = subprocess.Popen(
                        logcat_command,
                        shell=True,
                        stdout=f,
                        stderr=subprocess.PIPE,
                        text=True,
                        encoding='utf-8',
                        errors='ignore'
                    )
                    
                    # 等待指定时间
                    time.sleep(duration)
                    process.terminate()
                    process.wait()
                    
            except Exception as e:
                self.log_message(f"logcat监控异常: {str(e)}", "ERROR")
        
        # 在后台线程中启动监控
        monitor_thread = threading.Thread(target=monitor_logcat)
        monitor_thread.daemon = True
        monitor_thread.start()
        
        return logcat_file

    def simulate_home_key_press(self):
        """模拟按下首页键"""
        self.log_message("模拟按下首页键...")
        
        # 记录按键时间
        press_time = datetime.now()
        self.log_message(f"首页键按下时间: {press_time.strftime('%H:%M:%S.%f')[:-3]}")
        
        # 发送首页键事件
        success, stdout, stderr = self.run_adb_command("shell input keyevent KEYCODE_HOME")
        
        if success:
            self.log_message("首页键事件发送成功")
            return press_time
        else:
            self.log_message("首页键事件发送失败", "ERROR")
            return None

    def wait_for_launcher_ready(self, timeout: int = 30) -> Optional[datetime]:
        """等待桌面完全加载"""
        self.log_message("等待当贝桌面完全加载...")
        
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            # 检查当前前台应用
            success, stdout, stderr = self.run_adb_command(
                "shell dumpsys window windows | grep -E 'mCurrentFocus|mFocusedApp'"
            )
            
            if success and ("launcher" in stdout.lower() or "home" in stdout.lower()):
                ready_time = datetime.now()
                self.log_message(f"桌面加载完成时间: {ready_time.strftime('%H:%M:%S.%f')[:-3]}")
                return ready_time
            
            time.sleep(0.5)
        
        self.log_message("等待桌面加载超时", "WARNING")
        return None

    def analyze_logcat_logs(self, logcat_file: str, press_time: datetime) -> Dict:
        """分析logcat日志"""
        self.log_message(f"分析logcat日志: {logcat_file}")
        
        analysis_result = {
            "key_events": [],
            "launcher_events": [],
            "system_events": [],
            "performance_issues": [],
            "timeline": []
        }
        
        try:
            with open(logcat_file, "r", encoding="utf-8", errors='ignore') as f:
                lines = f.readlines()
            
            # 关键词过滤
            key_patterns = [
                r"KeyEvent.*KEYCODE_HOME",
                r"ActivityManager.*START.*home",
                r"ActivityManager.*launcher",
                r"WindowManager.*home",
                r"InputDispatcher",
                r"ActivityTaskManager",
                r"PackageManager.*launcher"
            ]
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                # 提取时间戳
                time_match = re.match(r'(\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})', line)
                if not time_match:
                    continue
                
                timestamp = time_match.group(1)
                
                # 检查关键事件
                for pattern in key_patterns:
                    if re.search(pattern, line, re.IGNORECASE):
                        event_info = {
                            "timestamp": timestamp,
                            "content": line,
                            "pattern": pattern
                        }
                        
                        if "KEYCODE_HOME" in line:
                            analysis_result["key_events"].append(event_info)
                        elif "launcher" in line.lower():
                            analysis_result["launcher_events"].append(event_info)
                        else:
                            analysis_result["system_events"].append(event_info)
                        
                        analysis_result["timeline"].append(event_info)
                        break
            
            self.log_message(f"找到 {len(analysis_result['key_events'])} 个按键事件")
            self.log_message(f"找到 {len(analysis_result['launcher_events'])} 个启动器事件")
            self.log_message(f"找到 {len(analysis_result['system_events'])} 个系统事件")
            
        except Exception as e:
            self.log_message(f"日志分析异常: {str(e)}", "ERROR")
        
        return analysis_result

    def generate_frida_scripts(self, analysis_result: Dict) -> List[str]:
        """基于日志分析结果生成Frida脚本"""
        self.log_message("生成Frida Hook脚本...")
        
        scripts = []
        
        # 基础的首页键处理脚本
        home_key_script = '''
// 首页键事件处理Hook脚本
// Copyright (c) 2025 By.举个🌰

Java.perform(function() {
    console.log("[*] 开始Hook首页键处理流程...");
    
    // Hook InputManagerService
    var InputManagerService = Java.use("com.android.server.input.InputManagerService");
    if (InputManagerService) {
        InputManagerService.notifyKey.implementation = function(deviceId, source, policyFlags, action, flags, keyCode, scanCode, metaState, downTime, eventTime) {
            if (keyCode == 3) { // KEYCODE_HOME
                console.log("[HOME_KEY] 检测到首页键事件");
                console.log("  时间: " + new Date().toISOString());
                console.log("  动作: " + (action == 0 ? "按下" : "抬起"));
                console.log("  设备ID: " + deviceId);
            }
            return this.notifyKey(deviceId, source, policyFlags, action, flags, keyCode, scanCode, metaState, downTime, eventTime);
        };
    }
    
    // Hook ActivityManagerService
    var ActivityManagerService = Java.use("com.android.server.am.ActivityManagerService");
    if (ActivityManagerService) {
        ActivityManagerService.startHomeActivityLocked.implementation = function(userId, reason) {
            console.log("[HOME_ACTIVITY] 启动首页Activity");
            console.log("  时间: " + new Date().toISOString());
            console.log("  用户ID: " + userId);
            console.log("  原因: " + reason);
            var start_time = Date.now();
            var result = this.startHomeActivityLocked(userId, reason);
            var end_time = Date.now();
            console.log("  耗时: " + (end_time - start_time) + "ms");
            return result;
        };
    }
});
'''
        
        scripts.append(("home_key_hook.js", home_key_script))

        # Launcher性能监控脚本
        launcher_script = '''
// Launcher性能监控Hook脚本
// Copyright (c) 2025 By.举个🌰

Java.perform(function() {
    console.log("[*] 开始Hook Launcher性能监控...");

    // Hook Activity生命周期
    var Activity = Java.use("android.app.Activity");
    Activity.onCreate.implementation = function(savedInstanceState) {
        var className = this.getClass().getName();
        if (className.toLowerCase().includes("launcher") || className.toLowerCase().includes("home")) {
            console.log("[LAUNCHER] Activity.onCreate: " + className);
            console.log("  时间: " + new Date().toISOString());
        }
        return this.onCreate(savedInstanceState);
    };

    Activity.onResume.implementation = function() {
        var className = this.getClass().getName();
        if (className.toLowerCase().includes("launcher") || className.toLowerCase().includes("home")) {
            console.log("[LAUNCHER] Activity.onResume: " + className);
            console.log("  时间: " + new Date().toISOString());
        }
        return this.onResume();
    };

    // Hook View绘制
    var View = Java.use("android.view.View");
    View.draw.implementation = function(canvas) {
        var className = this.getClass().getName();
        if (className.toLowerCase().includes("launcher")) {
            var start_time = Date.now();
            this.draw(canvas);
            var end_time = Date.now();
            if (end_time - start_time > 16) { // 超过16ms可能影响流畅度
                console.log("[PERFORMANCE] View绘制耗时: " + (end_time - start_time) + "ms, View: " + className);
            }
            return;
        }
        return this.draw(canvas);
    };
});
'''

        scripts.append(("launcher_performance_hook.js", launcher_script))

        # 系统服务监控脚本
        system_service_script = '''
// 系统服务监控Hook脚本
// Copyright (c) 2025 By.举个🌰

Java.perform(function() {
    console.log("[*] 开始Hook系统服务监控...");

    // Hook PackageManagerService
    var PackageManagerService = Java.use("com.android.server.pm.PackageManagerService");
    if (PackageManagerService) {
        PackageManagerService.resolveIntent.implementation = function(intent, resolvedType, flags, userId) {
            if (intent && intent.getAction() && intent.getAction().equals("android.intent.action.MAIN")) {
                console.log("[SYSTEM] PackageManager解析Intent");
                console.log("  时间: " + new Date().toISOString());
                console.log("  Intent: " + intent.toString());
            }
            return this.resolveIntent(intent, resolvedType, flags, userId);
        };
    }

    // Hook WindowManagerService
    var WindowManagerService = Java.use("com.android.server.wm.WindowManagerService");
    if (WindowManagerService) {
        WindowManagerService.addWindow.implementation = function(session, client, seq, attrs, viewVisibility, displayId, outContentInsets, outStableInsets, outInputChannel) {
            if (attrs && attrs.getTitle() && attrs.getTitle().toString().toLowerCase().includes("launcher")) {
                console.log("[SYSTEM] WindowManager添加Launcher窗口");
                console.log("  时间: " + new Date().toISOString());
                console.log("  标题: " + attrs.getTitle());
            }
            return this.addWindow(session, client, seq, attrs, viewVisibility, displayId, outContentInsets, outStableInsets, outInputChannel);
        };
    }
});
'''

        scripts.append(("system_service_hook.js", system_service_script))

        return scripts

    def save_frida_scripts(self, scripts: List[Tuple[str, str]]) -> List[str]:
        """保存Frida脚本到文件"""
        script_files = []

        for filename, content in scripts:
            script_path = os.path.join(self.output_dir, filename)
            with open(script_path, "w", encoding="utf-8") as f:
                f.write(content)

            script_files.append(script_path)
            self.log_message(f"Frida脚本已保存: {script_path}")

        return script_files

    def run_frida_analysis(self, script_files: List[str], target_process: str = "system_server") -> Dict:
        """运行Frida分析"""
        self.log_message(f"开始Frida动态分析，目标进程: {target_process}")

        frida_results = {}

        for script_file in script_files:
            self.log_message(f"执行Frida脚本: {script_file}")

            # 构建frida命令
            frida_command = f"frida -U -l {script_file} {target_process}"

            try:
                # 创建输出文件
                output_file = os.path.join(self.output_dir, f"frida_output_{os.path.basename(script_file)}.log")

                self.log_message(f"Frida输出将保存到: {output_file}")
                self.log_message("请在另一个终端中手动执行以下命令:")
                self.log_message(f"  {frida_command}")
                self.log_message("然后按首页键进行测试")

                frida_results[script_file] = {
                    "command": frida_command,
                    "output_file": output_file,
                    "status": "需要手动执行"
                }

            except Exception as e:
                self.log_message(f"Frida脚本执行异常: {str(e)}", "ERROR")
                frida_results[script_file] = {
                    "error": str(e),
                    "status": "执行失败"
                }

        return frida_results

    def generate_performance_report(self, device_info: Dict, logcat_analysis: Dict,
                                  press_time: datetime, ready_time: Optional[datetime]) -> str:
        """生成性能分析报告"""
        self.log_message("生成性能分析报告...")

        report_file = os.path.join(self.output_dir, f"performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md")

        # 计算响应时间
        response_time = "未知"
        if press_time and ready_time:
            delta = ready_time - press_time
            response_time = f"{delta.total_seconds():.3f}秒"

        report_content = f"""# 晶晨机顶盒按首页键性能分析报告

**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**版权所有**: © 2025 By.举个🌰

## 1. 设备信息

| 项目 | 值 |
|------|-----|
| 设备IP | {self.device_ip} |
| Android版本 | {device_info.get('android_version', '未知')} |
| SDK版本 | {device_info.get('sdk_version', '未知')} |
| 设备型号 | {device_info.get('device_model', '未知')} |
| 制造商 | {device_info.get('manufacturer', '未知')} |
| CPU架构 | {device_info.get('cpu_abi', '未知')} |

## 2. 性能测试结果

| 指标 | 值 |
|------|-----|
| 首页键按下时间 | {press_time.strftime('%H:%M:%S.%f')[:-3] if press_time else '未记录'} |
| 桌面加载完成时间 | {ready_time.strftime('%H:%M:%S.%f')[:-3] if ready_time else '未检测到'} |
| **总响应时间** | **{response_time}** |

## 3. 日志分析结果

### 3.1 按键事件统计
- 检测到按键事件: {len(logcat_analysis.get('key_events', []))} 个
- 启动器相关事件: {len(logcat_analysis.get('launcher_events', []))} 个
- 系统服务事件: {len(logcat_analysis.get('system_events', []))} 个

### 3.2 关键事件时间线
"""

        # 添加事件时间线
        timeline = logcat_analysis.get('timeline', [])
        if timeline:
            report_content += "\n| 时间 | 事件类型 | 描述 |\n|------|----------|------|\n"
            for event in timeline[:20]:  # 只显示前20个事件
                event_type = "按键事件" if "KEYCODE_HOME" in event['content'] else \
                           "启动器事件" if "launcher" in event['content'].lower() else "系统事件"
                description = event['content'][:100] + "..." if len(event['content']) > 100 else event['content']
                report_content += f"| {event['timestamp']} | {event_type} | {description} |\n"

        report_content += f"""

## 4. 问题分析

### 4.1 可能的性能瓶颈
"""

        # 分析可能的问题
        issues = []
        if ready_time and press_time:
            response_seconds = (ready_time - press_time).total_seconds()
            if response_seconds > 3:
                issues.append("响应时间过长，超过3秒")
            if response_seconds > 5:
                issues.append("严重性能问题，响应时间超过5秒")

        launcher_events = logcat_analysis.get('launcher_events', [])
        if len(launcher_events) > 10:
            issues.append("启动器事件过多，可能存在重复初始化")

        if not issues:
            issues.append("未检测到明显的性能问题")

        for i, issue in enumerate(issues, 1):
            report_content += f"{i}. {issue}\n"

        report_content += f"""

### 4.2 建议的优化方向

1. **检查启动器应用**
   - 分析当贝桌面的启动流程
   - 检查是否有不必要的初始化操作
   - 优化资源加载策略

2. **系统服务优化**
   - 检查ActivityManagerService的响应速度
   - 分析WindowManagerService的窗口创建流程
   - 优化PackageManagerService的Intent解析

3. **内存和存储优化**
   - 检查可用内存状态
   - 清理不必要的后台进程
   - 优化存储I/O操作

## 5. Frida动态分析

### 5.1 生成的Hook脚本
- `home_key_hook.js`: 首页键事件处理监控
- `launcher_performance_hook.js`: Launcher性能监控
- `system_service_hook.js`: 系统服务监控

### 5.2 使用方法
1. 确保Frida服务器在设备上运行
2. 在终端中执行生成的Frida命令
3. 按首页键进行测试
4. 观察Hook输出的性能数据

## 6. 后续分析建议

1. **深入代码分析**
   - 使用生成的Frida脚本进行实时监控
   - 分析具体的函数调用耗时
   - 定位性能瓶颈的具体代码位置

2. **系统资源监控**
   - 监控CPU使用率
   - 检查内存使用情况
   - 分析I/O操作频率

3. **对比测试**
   - 与其他设备进行对比
   - 测试不同时间段的性能表现
   - 分析系统负载对响应时间的影响

---

**报告生成工具**: 晶晨机顶盒按首页键性能分析工具
**版权所有**: © 2025 By.举个🌰
"""

        # 保存报告
        with open(report_file, "w", encoding="utf-8") as f:
            f.write(report_content)

        self.log_message(f"性能分析报告已生成: {report_file}")
        return report_file

    def run_complete_analysis(self) -> bool:
        """运行完整的性能分析流程"""
        self.log_message("开始完整的性能分析流程...")

        try:
            # 1. 检查ADB连接
            if not self.check_adb_connection():
                self.log_message("ADB连接失败，无法继续分析", "ERROR")
                return False

            # 2. 获取设备信息
            device_info = self.get_device_info()

            # 3. 准备日志监控
            self.log_message("\n" + "="*50)
            self.log_message("准备开始日志监控...")
            self.log_message("请在看到'开始按首页键测试'提示后，按下设备的首页键")
            self.log_message("="*50)

            # 等待用户准备
            input("按回车键开始监控...")

            # 4. 开始logcat监控
            logcat_file = self.start_logcat_monitoring(duration=20)

            # 等待监控启动
            time.sleep(2)

            # 5. 提示用户按首页键
            self.log_message("\n" + "!"*50)
            self.log_message("开始按首页键测试！")
            self.log_message("请现在按下设备的首页键")
            self.log_message("!"*50)

            # 6. 模拟按首页键（可选，也可以手动按）
            press_time = self.simulate_home_key_press()

            # 7. 等待桌面加载完成
            ready_time = self.wait_for_launcher_ready(timeout=15)

            # 8. 等待日志收集完成
            self.log_message("等待日志收集完成...")
            time.sleep(5)

            # 9. 分析日志
            logcat_analysis = self.analyze_logcat_logs(logcat_file, press_time)

            # 10. 生成Frida脚本
            frida_scripts = self.generate_frida_scripts(logcat_analysis)
            script_files = self.save_frida_scripts(frida_scripts)

            # 11. 运行Frida分析（生成命令）
            frida_results = self.run_frida_analysis(script_files)

            # 12. 生成性能报告
            report_file = self.generate_performance_report(
                device_info, logcat_analysis, press_time, ready_time
            )

            # 13. 输出总结
            self.log_message("\n" + "="*60)
            self.log_message("分析完成！")
            self.log_message(f"输出目录: {self.output_dir}")
            self.log_message(f"性能报告: {report_file}")
            self.log_message(f"日志文件: {logcat_file}")
            self.log_message("Frida脚本:")
            for script_file in script_files:
                self.log_message(f"  - {script_file}")

            if press_time and ready_time:
                response_time = (ready_time - press_time).total_seconds()
                self.log_message(f"\n首页键响应时间: {response_time:.3f}秒")

                if response_time > 3:
                    self.log_message("⚠️  响应时间较长，建议进行优化", "WARNING")
                else:
                    self.log_message("✅ 响应时间正常")

            self.log_message("="*60)

            return True

        except KeyboardInterrupt:
            self.log_message("用户中断分析", "WARNING")
            return False
        except Exception as e:
            self.log_message(f"分析过程中发生异常: {str(e)}", "ERROR")
            return False

    def run_logcat_only_analysis(self, duration: int = 30) -> bool:
        """仅运行logcat分析"""
        self.log_message("开始logcat分析模式...")

        try:
            # 检查ADB连接
            if not self.check_adb_connection():
                return False

            # 获取设备信息
            device_info = self.get_device_info()

            # 开始监控
            self.log_message(f"开始{duration}秒的logcat监控...")
            self.log_message("请在监控期间按首页键进行测试")

            logcat_file = self.start_logcat_monitoring(duration)

            # 等待监控完成
            time.sleep(duration + 2)

            # 分析日志
            press_time = datetime.now()  # 使用当前时间作为参考
            logcat_analysis = self.analyze_logcat_logs(logcat_file, press_time)

            # 生成报告
            report_file = self.generate_performance_report(
                device_info, logcat_analysis, press_time, None
            )

            self.log_message(f"logcat分析完成，报告: {report_file}")
            return True

        except Exception as e:
            self.log_message(f"logcat分析异常: {str(e)}", "ERROR")
            return False

    def run_frida_only_analysis(self) -> bool:
        """仅生成Frida脚本"""
        self.log_message("生成Frida分析脚本...")

        try:
            # 生成基础的分析结果
            basic_analysis = {
                "key_events": [],
                "launcher_events": [],
                "system_events": [],
                "performance_issues": [],
                "timeline": []
            }

            # 生成Frida脚本
            frida_scripts = self.generate_frida_scripts(basic_analysis)
            script_files = self.save_frida_scripts(frida_scripts)

            # 生成使用说明
            usage_file = os.path.join(self.output_dir, "frida_usage.md")
            usage_content = f"""# Frida脚本使用说明

**版权所有**: © 2025 By.举个🌰

## 生成的脚本文件

1. **home_key_hook.js** - 首页键事件处理监控
2. **launcher_performance_hook.js** - Launcher性能监控
3. **system_service_hook.js** - 系统服务监控

## 使用方法

### 1. 确保Frida环境
```bash
# 检查Frida服务器是否运行
adb -s {self.device_id} shell ps | grep frida
```

### 2. 执行Hook脚本
```bash
# Hook系统服务（需要root权限）
frida -U -l home_key_hook.js system_server

# Hook Launcher应用
frida -U -l launcher_performance_hook.js com.dangbei.tvlauncher

# 同时Hook多个脚本
frida -U -l home_key_hook.js -l launcher_performance_hook.js system_server
```

### 3. 测试流程
1. 启动Frida Hook脚本
2. 按下设备首页键
3. 观察终端输出的性能数据
4. 分析Hook结果

### 4. 输出说明
- `[HOME_KEY]`: 首页键事件相关信息
- `[LAUNCHER]`: Launcher应用相关信息
- `[SYSTEM]`: 系统服务相关信息
- `[PERFORMANCE]`: 性能问题警告

## 注意事项
- 需要root权限才能Hook系统服务
- 某些系统可能有SELinux限制
- 建议在测试环境中使用
"""

            with open(usage_file, "w", encoding="utf-8") as f:
                f.write(usage_content)

            self.log_message("Frida脚本生成完成:")
            for script_file in script_files:
                self.log_message(f"  - {script_file}")
            self.log_message(f"使用说明: {usage_file}")

            return True

        except Exception as e:
            self.log_message(f"Frida脚本生成异常: {str(e)}", "ERROR")
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="晶晨机顶盒按首页键响应缓慢问题分析工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 完整分析（推荐）
  python homekey_performance_analyzer.py --ip *************** --complete

  # 仅logcat分析
  python homekey_performance_analyzer.py --ip *************** --logcat-only --duration 30

  # 仅生成Frida脚本
  python homekey_performance_analyzer.py --frida-only

版权所有 © 2025 By.举个🌰
        """
    )

    parser.add_argument(
        "--ip",
        default="***************",
        help="设备IP地址 (默认: ***************)"
    )

    parser.add_argument(
        "--port",
        type=int,
        default=5555,
        help="ADB端口 (默认: 5555)"
    )

    parser.add_argument(
        "--complete",
        action="store_true",
        help="运行完整的性能分析流程（默认模式）"
    )

    parser.add_argument(
        "--logcat-only",
        action="store_true",
        help="仅运行logcat日志分析"
    )

    parser.add_argument(
        "--frida-only",
        action="store_true",
        help="仅生成Frida Hook脚本"
    )

    parser.add_argument(
        "--duration",
        type=int,
        default=30,
        help="logcat监控持续时间（秒，默认: 30）"
    )

    args = parser.parse_args()

    # 创建分析器实例
    analyzer = HomeKeyPerformanceAnalyzer(device_ip=args.ip, adb_port=args.port)

    try:
        # 根据参数选择运行模式
        if args.logcat_only:
            success = analyzer.run_logcat_only_analysis(duration=args.duration)
        elif args.frida_only:
            success = analyzer.run_frida_only_analysis()
        else:
            # 默认运行完整分析
            success = analyzer.run_complete_analysis()

        if success:
            analyzer.log_message("\n🎉 分析完成！")
            analyzer.log_message(f"📁 输出目录: {analyzer.output_dir}")
            return 0
        else:
            analyzer.log_message("\n❌ 分析失败！", "ERROR")
            return 1

    except KeyboardInterrupt:
        analyzer.log_message("\n⚠️  用户中断操作", "WARNING")
        return 1
    except Exception as e:
        analyzer.log_message(f"\n💥 程序异常: {str(e)}", "ERROR")
        return 1


if __name__ == "__main__":
    exit(main())

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
晶晨机顶盒按首页键性能分析工具 - 启动脚本
自动创建虚拟环境并运行分析工具

Copyright (c) 2025 By.举个🌰
All rights reserved.
"""

import os
import sys
import subprocess
import platform


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    return True


def create_virtual_environment():
    """创建虚拟环境"""
    venv_path = "analysis_env"
    
    if os.path.exists(venv_path):
        print(f"✅ 虚拟环境已存在: {venv_path}")
        return venv_path
    
    print(f"🔧 创建虚拟环境: {venv_path}")
    try:
        subprocess.run([sys.executable, "-m", "venv", venv_path], check=True)
        print("✅ 虚拟环境创建成功")
        return venv_path
    except subprocess.CalledProcessError as e:
        print(f"❌ 虚拟环境创建失败: {e}")
        return None


def get_activation_command(venv_path):
    """获取虚拟环境激活命令"""
    system = platform.system().lower()
    
    if system == "windows":
        return os.path.join(venv_path, "Scripts", "python.exe")
    else:
        return os.path.join(venv_path, "bin", "python")


def install_dependencies(python_path):
    """安装依赖包"""
    print("📦 检查并安装依赖包...")
    
    # 检查是否需要安装依赖
    try:
        subprocess.run([python_path, "-c", "import argparse"], check=True, capture_output=True)
        print("✅ 基础依赖已满足")
        return True
    except subprocess.CalledProcessError:
        pass
    
    # 如果有requirements.txt文件，使用它
    if os.path.exists("requirements.txt"):
        try:
            subprocess.run([python_path, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
            print("✅ 依赖安装成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 依赖安装失败: {e}")
            return False
    
    print("✅ 无需额外依赖")
    return True


def run_analyzer(python_path, args):
    """运行分析工具"""
    print("🚀 启动性能分析工具...")
    print("=" * 60)
    
    try:
        # 构建命令
        cmd = [python_path, "homekey_performance_analyzer.py"] + args
        
        # 运行分析工具
        result = subprocess.run(cmd, check=False)
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ 运行分析工具失败: {e}")
        return False


def main():
    """主函数"""
    print("🔍 晶晨机顶盒按首页键性能分析工具")
    print("版权所有 © 2025 By.举个🌰")
    print("=" * 60)
    
    # 检查Python版本
    if not check_python_version():
        return 1
    
    # 创建虚拟环境
    venv_path = create_virtual_environment()
    if not venv_path:
        return 1
    
    # 获取Python路径
    python_path = get_activation_command(venv_path)
    
    # 安装依赖
    if not install_dependencies(python_path):
        return 1
    
    # 解析命令行参数
    args = sys.argv[1:] if len(sys.argv) > 1 else ["--complete"]
    
    # 如果没有指定IP，使用默认值
    if not any(arg.startswith("--ip") for arg in args):
        print("💡 使用默认设备IP: ***************")
        print("   如需修改，请使用: --ip YOUR_DEVICE_IP")
        print()
    
    # 运行分析工具
    success = run_analyzer(python_path, args)
    
    if success:
        print("\n🎉 分析完成！")
        print("📁 请查看 analysis_output 目录中的结果文件")
        return 0
    else:
        print("\n❌ 分析失败！")
        return 1


if __name__ == "__main__":
    try:
        exit(main())
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        exit(1)
    except Exception as e:
        print(f"\n💥 程序异常: {e}")
        exit(1)

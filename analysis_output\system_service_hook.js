
// 系统服务监控Hook脚本
// Copyright (c) 2025 By.举个🌰

Java.perform(function() {
    console.log("[*] 开始Hook系统服务监控...");

    // Hook PackageManagerService
    var PackageManagerService = Java.use("com.android.server.pm.PackageManagerService");
    if (PackageManagerService) {
        PackageManagerService.resolveIntent.implementation = function(intent, resolvedType, flags, userId) {
            if (intent && intent.getAction() && intent.getAction().equals("android.intent.action.MAIN")) {
                console.log("[SYSTEM] PackageManager解析Intent");
                console.log("  时间: " + new Date().toISOString());
                console.log("  Intent: " + intent.toString());
            }
            return this.resolveIntent(intent, resolvedType, flags, userId);
        };
    }

    // Hook WindowManagerService
    var WindowManagerService = Java.use("com.android.server.wm.WindowManagerService");
    if (WindowManagerService) {
        WindowManagerService.addWindow.implementation = function(session, client, seq, attrs, viewVisibility, displayId, outContentInsets, outStableInsets, outInputChannel) {
            if (attrs && attrs.getTitle() && attrs.getTitle().toString().toLowerCase().includes("launcher")) {
                console.log("[SYSTEM] WindowManager添加Launcher窗口");
                console.log("  时间: " + new Date().toISOString());
                console.log("  标题: " + attrs.getTitle());
            }
            return this.addWindow(session, client, seq, attrs, viewVisibility, displayId, outContentInsets, outStableInsets, outInputChannel);
        };
    }
});

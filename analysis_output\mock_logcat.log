01-30 12:00:01.123  1234  1234 I InputDispatcher: Delivering key to (12345): action=0x0, keyCode=3
01-30 12:00:01.125  1234  1234 I ActivityManager: START u0 {act=android.intent.action.MAIN cat=[android.intent.category.HOME] flg=0x10200000} from uid 1000
01-30 12:00:01.456  1234  1234 I ActivityTaskManager: Displayed com.dangbei.tvlauncher/.MainActivity: +234ms
01-30 12:00:01.789  1234  1234 I WindowManager: Adding window Window{12345 u0 com.dangbei.tvlauncher/com.dangbei.tvlauncher.MainActivity} at 1
01-30 12:00:02.123  1234  1234 I PackageManager: Resolving activity: Intent { act=android.intent.action.MAIN cat=[android.intent.category.HOME] }

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
晶晨机顶盒按首页键性能分析工具 - 演示脚本
展示如何使用分析工具的各种功能

Copyright (c) 2025 By.举个🌰
All rights reserved.
"""

import os
import sys
import time
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.getcwd())

try:
    from homekey_performance_analyzer import HomeKeyPerformanceAnalyzer
except ImportError as e:
    print(f"❌ 无法导入分析器模块: {e}")
    print("请确保 homekey_performance_analyzer.py 文件存在")
    sys.exit(1)


def demo_device_info():
    """演示设备信息获取"""
    print("🔍 演示：设备信息获取")
    print("-" * 40)
    
    # 创建分析器实例
    analyzer = HomeKeyPerformanceAnalyzer(device_ip="***************")
    
    # 检查连接
    if analyzer.check_adb_connection():
        # 获取设备信息
        device_info = analyzer.get_device_info()
        
        print("📱 设备信息:")
        for key, value in device_info.items():
            print(f"   {key}: {value}")
    else:
        print("❌ 设备连接失败，使用模拟数据演示")
        # 模拟设备信息
        device_info = {
            "android_version": "9",
            "sdk_version": "28", 
            "device_model": "晶晨机顶盒",
            "manufacturer": "Amlogic",
            "cpu_abi": "arm64-v8a"
        }
        
        print("📱 模拟设备信息:")
        for key, value in device_info.items():
            print(f"   {key}: {value}")
    
    return device_info


def demo_frida_scripts():
    """演示Frida脚本生成"""
    print("\n🔧 演示：Frida脚本生成")
    print("-" * 40)
    
    analyzer = HomeKeyPerformanceAnalyzer(device_ip="***************")
    
    # 创建模拟的分析结果
    mock_analysis = {
        "key_events": [
            {"timestamp": "01-30 12:00:01.123", "content": "KeyEvent KEYCODE_HOME", "pattern": "KEYCODE_HOME"}
        ],
        "launcher_events": [
            {"timestamp": "01-30 12:00:01.456", "content": "ActivityManager START launcher", "pattern": "launcher"}
        ],
        "system_events": [
            {"timestamp": "01-30 12:00:01.789", "content": "WindowManager home window", "pattern": "WindowManager"}
        ],
        "performance_issues": [],
        "timeline": []
    }
    
    # 生成Frida脚本
    scripts = analyzer.generate_frida_scripts(mock_analysis)
    script_files = analyzer.save_frida_scripts(scripts)
    
    print("✅ 生成的Frida脚本:")
    for script_file in script_files:
        print(f"   📄 {script_file}")
        
        # 显示脚本内容的前几行
        try:
            with open(script_file, "r", encoding="utf-8") as f:
                lines = f.readlines()[:10]  # 只显示前10行
                print("      内容预览:")
                for i, line in enumerate(lines, 1):
                    print(f"      {i:2d}: {line.rstrip()}")
                if len(lines) >= 10:
                    print("      ... (更多内容)")
                print()
        except Exception as e:
            print(f"      ❌ 读取文件失败: {e}")


def demo_log_analysis():
    """演示日志分析功能"""
    print("\n📊 演示：日志分析功能")
    print("-" * 40)
    
    analyzer = HomeKeyPerformanceAnalyzer(device_ip="***************")
    
    # 创建模拟的logcat日志文件
    mock_logcat_file = os.path.join(analyzer.output_dir, "mock_logcat.log")
    
    mock_logcat_content = """01-30 12:00:01.123  1234  1234 I InputDispatcher: Delivering key to (12345): action=0x0, keyCode=3
01-30 12:00:01.125  1234  1234 I ActivityManager: START u0 {act=android.intent.action.MAIN cat=[android.intent.category.HOME] flg=0x10200000} from uid 1000
01-30 12:00:01.456  1234  1234 I ActivityTaskManager: Displayed com.dangbei.tvlauncher/.MainActivity: +234ms
01-30 12:00:01.789  1234  1234 I WindowManager: Adding window Window{12345 u0 com.dangbei.tvlauncher/com.dangbei.tvlauncher.MainActivity} at 1
01-30 12:00:02.123  1234  1234 I PackageManager: Resolving activity: Intent { act=android.intent.action.MAIN cat=[android.intent.category.HOME] }
"""
    
    # 写入模拟日志
    with open(mock_logcat_file, "w", encoding="utf-8") as f:
        f.write(mock_logcat_content)
    
    print(f"📝 创建模拟日志文件: {mock_logcat_file}")
    
    # 分析日志
    press_time = datetime.now()
    analysis_result = analyzer.analyze_logcat_logs(mock_logcat_file, press_time)
    
    print("📈 分析结果:")
    print(f"   按键事件: {len(analysis_result['key_events'])} 个")
    print(f"   启动器事件: {len(analysis_result['launcher_events'])} 个")
    print(f"   系统事件: {len(analysis_result['system_events'])} 个")
    print(f"   时间线事件: {len(analysis_result['timeline'])} 个")
    
    # 显示时间线
    if analysis_result['timeline']:
        print("\n⏰ 事件时间线:")
        for i, event in enumerate(analysis_result['timeline'][:5], 1):  # 只显示前5个
            print(f"   {i}. {event['timestamp']} - {event['content'][:50]}...")


def demo_performance_report():
    """演示性能报告生成"""
    print("\n📋 演示：性能报告生成")
    print("-" * 40)
    
    analyzer = HomeKeyPerformanceAnalyzer(device_ip="***************")
    
    # 模拟数据
    device_info = {
        "android_version": "9",
        "sdk_version": "28",
        "device_model": "晶晨机顶盒演示",
        "manufacturer": "Amlogic",
        "cpu_abi": "arm64-v8a"
    }
    
    logcat_analysis = {
        "key_events": [{"timestamp": "12:00:01.123", "content": "KEYCODE_HOME pressed"}],
        "launcher_events": [{"timestamp": "12:00:01.456", "content": "Launcher started"}],
        "system_events": [{"timestamp": "12:00:01.789", "content": "Window created"}],
        "timeline": [
            {"timestamp": "12:00:01.123", "content": "Home key pressed"},
            {"timestamp": "12:00:01.456", "content": "Launcher activity started"},
            {"timestamp": "12:00:02.123", "content": "UI fully loaded"}
        ]
    }
    
    press_time = datetime.now()
    ready_time = datetime.now()
    ready_time = ready_time.replace(microsecond=ready_time.microsecond + 500000)  # 添加0.5秒延迟
    
    # 生成报告
    report_file = analyzer.generate_performance_report(
        device_info, logcat_analysis, press_time, ready_time
    )
    
    print(f"📄 性能报告已生成: {report_file}")
    
    # 显示报告内容的前几行
    try:
        with open(report_file, "r", encoding="utf-8") as f:
            lines = f.readlines()[:20]  # 显示前20行
            print("\n📖 报告内容预览:")
            for i, line in enumerate(lines, 1):
                print(f"   {i:2d}: {line.rstrip()}")
            print("   ... (更多内容请查看完整报告)")
    except Exception as e:
        print(f"❌ 读取报告失败: {e}")


def main():
    """主演示函数"""
    print("🎭 晶晨机顶盒按首页键性能分析工具 - 功能演示")
    print("版权所有 © 2025 By.举个🌰")
    print("=" * 60)
    
    try:
        # 演示各个功能
        demo_device_info()
        demo_frida_scripts()
        demo_log_analysis()
        demo_performance_report()
        
        print("\n" + "=" * 60)
        print("🎉 演示完成！")
        print("💡 要运行实际分析，请使用:")
        print("   python run_analysis.py --complete")
        print("📁 演示生成的文件保存在 analysis_output 目录中")
        
    except Exception as e:
        print(f"\n💥 演示过程中发生异常: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    try:
        exit(main())
    except KeyboardInterrupt:
        print("\n⚠️  演示被用户中断")
        exit(1)

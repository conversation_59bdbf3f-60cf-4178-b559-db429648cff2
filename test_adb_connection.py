#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ADB连接测试脚本
测试与晶晨机顶盒的ADB连接

Copyright (c) 2025 By.举个🌰
"""

import subprocess
import sys


def test_adb_connection(device_ip="***************", port=5555):
    """测试ADB连接"""
    device_id = f"{device_ip}:{port}"
    
    print("🔍 晶晨机顶盒ADB连接测试")
    print("版权所有 © 2025 By.举个🌰")
    print("=" * 40)
    print(f"设备IP: {device_ip}")
    print(f"端口: {port}")
    print("=" * 40)
    
    # 1. 检查ADB工具
    print("\n1. 检查ADB工具...")
    try:
        result = subprocess.run(["adb", "version"], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✅ ADB工具可用")
            print(f"   版本: {result.stdout.strip().split()[4]}")
        else:
            print("❌ ADB工具不可用")
            return False
    except Exception as e:
        print(f"❌ ADB工具检查失败: {e}")
        return False
    
    # 2. 尝试连接设备
    print(f"\n2. 连接设备 {device_id}...")
    try:
        result = subprocess.run([
            "adb", "connect", device_id
        ], capture_output=True, text=True, timeout=10)
        
        print(f"   连接结果: {result.stdout.strip()}")
        
        if "connected" in result.stdout.lower() or "already connected" in result.stdout.lower():
            print("✅ 设备连接成功")
        else:
            print("❌ 设备连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 连接异常: {e}")
        return False
    
    # 3. 测试设备响应
    print("\n3. 测试设备响应...")
    try:
        result = subprocess.run([
            "adb", "-s", device_id, "shell", "echo", "hello"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 and "hello" in result.stdout:
            print("✅ 设备响应正常")
        else:
            print("❌ 设备无响应")
            return False
            
    except Exception as e:
        print(f"❌ 响应测试异常: {e}")
        return False
    
    # 4. 获取设备信息
    print("\n4. 获取设备信息...")
    info_commands = {
        "Android版本": ["shell", "getprop", "ro.build.version.release"],
        "设备型号": ["shell", "getprop", "ro.product.model"],
        "制造商": ["shell", "getprop", "ro.product.manufacturer"],
        "CPU架构": ["shell", "getprop", "ro.product.cpu.abi"]
    }
    
    for name, cmd in info_commands.items():
        try:
            result = subprocess.run([
                "adb", "-s", device_id
            ] + cmd, capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0:
                value = result.stdout.strip()
                print(f"   {name}: {value}")
            else:
                print(f"   {name}: 获取失败")
                
        except Exception as e:
            print(f"   {name}: 异常 - {e}")
    
    # 5. 测试logcat
    print("\n5. 测试logcat功能...")
    try:
        # 清除日志
        subprocess.run([
            "adb", "-s", device_id, "logcat", "-c"
        ], capture_output=True, timeout=5)
        
        # 测试日志输出
        result = subprocess.run([
            "adb", "-s", device_id, "logcat", "-d", "-t", "5"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            print(f"✅ logcat功能正常，获取到 {len(lines)} 行日志")
        else:
            print("❌ logcat功能异常")
            
    except Exception as e:
        print(f"❌ logcat测试异常: {e}")
    
    print("\n" + "=" * 40)
    print("🎉 ADB连接测试完成！")
    print("💡 设备已准备好进行性能分析")
    return True


def main():
    """主函数"""
    # 获取设备IP
    if len(sys.argv) > 1:
        device_ip = sys.argv[1]
    else:
        device_ip = "***************"
    
    success = test_adb_connection(device_ip)
    
    if success:
        print("\n🚀 可以开始使用性能分析工具:")
        print("   python run_analysis.py --complete")
        return 0
    else:
        print("\n❌ 连接测试失败，请检查:")
        print("   1. 设备IP地址是否正确")
        print("   2. 设备是否开启ADB调试")
        print("   3. 网络连接是否正常")
        return 1


if __name__ == "__main__":
    exit(main())

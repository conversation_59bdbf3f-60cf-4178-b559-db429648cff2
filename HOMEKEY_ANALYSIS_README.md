# 晶晨机顶盒按首页键性能分析工具

**版权所有 © 2025 By.举个🌰**

## 🎯 工具简介

这是一个专门用于分析晶晨机顶盒（Android 9系统）按首页键响应缓慢问题的自动化分析工具。工具通过ADB日志抓取和Frida动态分析，帮助定位造成首页键响应延迟的根本原因。

## 📋 功能特性

- 🔗 **自动ADB连接** - 自动连接指定IP的Android设备
- 📊 **实时日志分析** - 抓取并分析按首页键时的系统日志
- ⏱️ **响应时间测量** - 精确测量从按键到桌面加载完成的时间
- 🔍 **Frida动态分析** - 生成专业的Hook脚本用于深入分析
- 📈 **性能报告生成** - 自动生成详细的性能分析报告
- 🎨 **多种分析模式** - 支持完整分析、仅日志分析、仅脚本生成等模式

## 🛠️ 系统要求

- **Python**: 3.7或更高版本
- **ADB工具**: 已安装并配置在系统PATH中
- **Android设备**: 已开启USB调试和网络ADB调试
- **Frida**: 设备上已安装并运行Frida服务器（用于动态分析）

## 🚀 快速开始

### 1. 准备设备
```bash
# 连接设备到ADB
adb connect ***************:5555

# 验证连接
adb devices
```

### 2. 运行分析（推荐方式）
```bash
# 使用自动化启动脚本（推荐）
python run_analysis.py

# 或指定设备IP
python run_analysis.py --ip *************** --complete
```

### 3. 手动运行（高级用户）
```bash
# 完整分析
python homekey_performance_analyzer.py --ip *************** --complete

# 仅logcat分析
python homekey_performance_analyzer.py --ip *************** --logcat-only --duration 30

# 仅生成Frida脚本
python homekey_performance_analyzer.py --frida-only
```

## 📖 使用说明

### 完整分析模式（推荐）

1. **启动工具**
   ```bash
   python run_analysis.py --complete
   ```

2. **按提示操作**
   - 工具会自动检查ADB连接
   - 获取设备信息
   - 提示准备开始监控

3. **执行测试**
   - 看到"开始按首页键测试"提示后
   - 按下设备的首页键
   - 等待当贝桌面完全加载

4. **查看结果**
   - 工具会自动生成分析报告
   - 所有文件保存在`analysis_output`目录

### 仅日志分析模式

适用于只需要分析系统日志的场景：

```bash
python run_analysis.py --logcat-only --duration 30
```

### 仅Frida脚本生成模式

适用于需要自定义Hook分析的场景：

```bash
python run_analysis.py --frida-only
```

## 📁 输出文件说明

分析完成后，会在`analysis_output`目录生成以下文件：

### 📊 分析报告
- `performance_report_YYYYMMDD_HHMMSS.md` - 详细的性能分析报告

### 📝 日志文件
- `logcat_YYYYMMDD_HHMMSS.log` - 原始logcat日志
- `homekey_analysis_YYYYMMDD_HHMMSS.log` - 工具运行日志

### 🔧 Frida脚本
- `home_key_hook.js` - 首页键事件处理监控脚本
- `launcher_performance_hook.js` - Launcher性能监控脚本
- `system_service_hook.js` - 系统服务监控脚本
- `frida_usage.md` - Frida脚本使用说明

## 🔍 Frida动态分析

### 使用生成的Hook脚本

1. **确保Frida服务器运行**
   ```bash
   adb shell ps | grep frida
   ```

2. **执行Hook脚本**
   ```bash
   # Hook系统服务
   frida -U -l analysis_output/home_key_hook.js system_server
   
   # Hook Launcher应用
   frida -U -l analysis_output/launcher_performance_hook.js com.dangbei.tvlauncher
   ```

3. **进行测试**
   - 启动Hook脚本后
   - 按下设备首页键
   - 观察终端输出的性能数据

### Hook输出说明
- `[HOME_KEY]` - 首页键事件相关信息
- `[LAUNCHER]` - Launcher应用相关信息
- `[SYSTEM]` - 系统服务相关信息
- `[PERFORMANCE]` - 性能问题警告

## 📈 性能分析报告

生成的报告包含以下内容：

1. **设备信息** - Android版本、设备型号等
2. **性能测试结果** - 响应时间测量
3. **日志分析结果** - 关键事件统计和时间线
4. **问题分析** - 可能的性能瓶颈
5. **优化建议** - 具体的优化方向
6. **Frida分析指导** - 深入分析的方法

## ⚠️ 注意事项

1. **权限要求**
   - Hook系统服务需要root权限
   - 某些系统可能有SELinux限制

2. **网络连接**
   - 确保PC和设备在同一网络
   - 检查防火墙设置

3. **设备状态**
   - 建议在设备空闲时进行测试
   - 避免其他应用干扰测试结果

## 🔧 故障排除

### ADB连接问题
```bash
# 检查设备连接
adb devices

# 重新连接
adb disconnect
adb connect ***************:5555
```

### Frida服务问题
```bash
# 检查Frida服务器
adb shell ps | grep frida

# 重启Frida服务器
adb shell killall frida-server
adb shell /data/local/tmp/frida-server &
```

### 权限问题
```bash
# 获取root权限
adb root

# 检查SELinux状态
adb shell getenforce
```

## 📞 技术支持

如遇到问题，请检查：
1. Python版本是否符合要求
2. ADB工具是否正确安装
3. 设备网络连接是否正常
4. Frida服务器是否正常运行

---

**开发工具**: Python 3 + ADB + Frida  
**适用设备**: 晶晨芯片Android机顶盒  
**版权所有**: © 2025 By.举个🌰

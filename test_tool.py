#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
晶晨机顶盒按首页键性能分析工具 - 测试脚本
用于测试工具的基本功能

Copyright (c) 2025 By.举个🌰
All rights reserved.
"""

import sys
import os
import subprocess
from datetime import datetime


def test_python_version():
    """测试Python版本"""
    print("🔍 检查Python版本...")
    version = sys.version_info
    print(f"   Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version >= (3, 7):
        print("   ✅ Python版本符合要求")
        return True
    else:
        print("   ❌ Python版本过低，需要3.7或更高版本")
        return False


def test_imports():
    """测试模块导入"""
    print("\n🔍 检查模块导入...")
    
    modules = [
        'subprocess', 'threading', 'time', 'os', 'json', 
        're', 'datetime', 'argparse'
    ]
    
    failed_modules = []
    
    for module in modules:
        try:
            __import__(module)
            print(f"   ✅ {module}")
        except ImportError:
            print(f"   ❌ {module}")
            failed_modules.append(module)
    
    if failed_modules:
        print(f"   ❌ 以下模块导入失败: {', '.join(failed_modules)}")
        return False
    else:
        print("   ✅ 所有模块导入成功")
        return True


def test_adb_command():
    """测试ADB命令"""
    print("\n🔍 检查ADB工具...")
    
    try:
        result = subprocess.run(
            ["adb", "version"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            version_info = result.stdout.strip().split('\n')[0]
            print(f"   ✅ ADB工具可用: {version_info}")
            return True
        else:
            print("   ❌ ADB命令执行失败")
            return False
            
    except FileNotFoundError:
        print("   ❌ 未找到ADB工具，请确保ADB已安装并配置在PATH中")
        return False
    except subprocess.TimeoutExpired:
        print("   ❌ ADB命令执行超时")
        return False
    except Exception as e:
        print(f"   ❌ ADB测试异常: {str(e)}")
        return False


def test_file_structure():
    """测试文件结构"""
    print("\n🔍 检查文件结构...")
    
    required_files = [
        "homekey_performance_analyzer.py",
        "run_analysis.py",
        "requirements.txt",
        "HOMEKEY_ANALYSIS_README.md"
    ]
    
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file}")
            missing_files.append(file)
    
    if missing_files:
        print(f"   ❌ 缺少文件: {', '.join(missing_files)}")
        return False
    else:
        print("   ✅ 所有必需文件存在")
        return True


def test_analyzer_import():
    """测试分析器导入"""
    print("\n🔍 检查分析器模块...")
    
    try:
        # 添加当前目录到Python路径
        sys.path.insert(0, os.getcwd())
        
        # 尝试导入分析器类
        from homekey_performance_analyzer import HomeKeyPerformanceAnalyzer
        
        print("   ✅ 分析器类导入成功")
        
        # 尝试创建实例
        analyzer = HomeKeyPerformanceAnalyzer(device_ip="127.0.0.1", adb_port=5555)
        print("   ✅ 分析器实例创建成功")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ 分析器导入失败: {str(e)}")
        return False
    except Exception as e:
        print(f"   ❌ 分析器测试异常: {str(e)}")
        return False


def test_output_directory():
    """测试输出目录创建"""
    print("\n🔍 检查输出目录创建...")
    
    try:
        test_dir = "test_output"
        os.makedirs(test_dir, exist_ok=True)
        
        if os.path.exists(test_dir):
            print(f"   ✅ 输出目录创建成功: {test_dir}")
            
            # 清理测试目录
            os.rmdir(test_dir)
            return True
        else:
            print("   ❌ 输出目录创建失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 输出目录测试异常: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("🧪 晶晨机顶盒按首页键性能分析工具 - 测试脚本")
    print("版权所有 © 2025 By.举个🌰")
    print("=" * 60)
    
    tests = [
        ("Python版本", test_python_version),
        ("模块导入", test_imports),
        ("ADB工具", test_adb_command),
        ("文件结构", test_file_structure),
        ("分析器模块", test_analyzer_import),
        ("输出目录", test_output_directory)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"   💥 测试异常: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！工具可以正常使用")
        print("\n💡 使用建议:")
        print("   python run_analysis.py --complete")
        return 0
    else:
        print("❌ 部分测试失败，请检查环境配置")
        print("\n🔧 可能的解决方案:")
        if passed < total:
            print("   1. 检查Python版本是否为3.7+")
            print("   2. 确保ADB工具已正确安装")
            print("   3. 检查所有文件是否完整")
        return 1


if __name__ == "__main__":
    try:
        exit(main())
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        exit(1)
    except Exception as e:
        print(f"\n💥 测试程序异常: {e}")
        exit(1)

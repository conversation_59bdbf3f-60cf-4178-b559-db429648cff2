// 晶晨机顶盒首页键性能分析 Frida Hook脚本
// Copyright (c) 2025 By.举个🌰

Java.perform(function() {
    console.log("[*] 开始Hook首页键处理流程...");
    console.log("[*] 设备型号: CM311-1e");
    console.log("[*] 当前Launcher: com.dangbei.tvlauncher");
    
    // Hook InputManagerService - 首页键事件处理
    try {
        var InputManagerService = Java.use("com.android.server.input.InputManagerService");
        InputManagerService.notifyKey.implementation = function(deviceId, source, policyFlags, action, flags, keyCode, scanCode, metaState, downTime, eventTime) {
            if (keyCode == 3) { // KEYCODE_HOME
                console.log("[HOME_KEY] ===== 首页键事件 =====");
                console.log("[HOME_KEY] 时间: " + new Date().toISOString());
                console.log("[HOME_KEY] 动作: " + (action == 0 ? "按下" : "抬起"));
                console.log("[HOME_KEY] 设备ID: " + deviceId);
                console.log("[HOME_KEY] 事件时间: " + eventTime);
                console.log("[HOME_KEY] ========================");
            }
            return this.notifyKey(deviceId, source, policyFlags, action, flags, keyCode, scanCode, metaState, downTime, eventTime);
        };
        console.log("[*] InputManagerService Hook成功");
    } catch(e) {
        console.log("[!] InputManagerService Hook失败: " + e);
    }
    
    // Hook ActivityManagerService - 首页Activity启动
    try {
        var ActivityManagerService = Java.use("com.android.server.am.ActivityManagerService");
        ActivityManagerService.startHomeActivityLocked.implementation = function(userId, reason) {
            console.log("[HOME_ACTIVITY] ===== 启动首页Activity =====");
            console.log("[HOME_ACTIVITY] 时间: " + new Date().toISOString());
            console.log("[HOME_ACTIVITY] 用户ID: " + userId);
            console.log("[HOME_ACTIVITY] 原因: " + reason);
            
            var start_time = Date.now();
            var result = this.startHomeActivityLocked(userId, reason);
            var end_time = Date.now();
            
            console.log("[HOME_ACTIVITY] 启动耗时: " + (end_time - start_time) + "ms");
            console.log("[HOME_ACTIVITY] =============================");
            return result;
        };
        console.log("[*] ActivityManagerService Hook成功");
    } catch(e) {
        console.log("[!] ActivityManagerService Hook失败: " + e);
    }
    
    // Hook WindowManagerService - 窗口管理
    try {
        var WindowManagerService = Java.use("com.android.server.wm.WindowManagerService");
        WindowManagerService.addWindow.implementation = function(session, client, seq, attrs, viewVisibility, displayId, outContentInsets, outStableInsets, outInputChannel) {
            if (attrs && attrs.getTitle() && attrs.getTitle().toString().toLowerCase().includes("launcher")) {
                console.log("[WINDOW] ===== 添加Launcher窗口 =====");
                console.log("[WINDOW] 时间: " + new Date().toISOString());
                console.log("[WINDOW] 标题: " + attrs.getTitle());
                console.log("[WINDOW] 显示ID: " + displayId);
                console.log("[WINDOW] ========================");
            }
            return this.addWindow(session, client, seq, attrs, viewVisibility, displayId, outContentInsets, outStableInsets, outInputChannel);
        };
        console.log("[*] WindowManagerService Hook成功");
    } catch(e) {
        console.log("[!] WindowManagerService Hook失败: " + e);
    }
    
    // Hook PackageManagerService - Intent解析
    try {
        var PackageManagerService = Java.use("com.android.server.pm.PackageManagerService");
        PackageManagerService.resolveIntent.implementation = function(intent, resolvedType, flags, userId) {
            if (intent && intent.getAction() && intent.getAction().equals("android.intent.action.MAIN")) {
                var categories = intent.getCategories();
                if (categories && categories.contains("android.intent.category.HOME")) {
                    console.log("[PACKAGE] ===== 解析HOME Intent =====");
                    console.log("[PACKAGE] 时间: " + new Date().toISOString());
                    console.log("[PACKAGE] Intent: " + intent.toString());
                    console.log("[PACKAGE] 用户ID: " + userId);
                    console.log("[PACKAGE] ========================");
                }
            }
            return this.resolveIntent(intent, resolvedType, flags, userId);
        };
        console.log("[*] PackageManagerService Hook成功");
    } catch(e) {
        console.log("[!] PackageManagerService Hook失败: " + e);
    }
    
    console.log("[*] 所有Hook设置完成，等待首页键事件...");
});

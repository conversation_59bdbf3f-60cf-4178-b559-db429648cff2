
// Launcher性能监控Hook脚本
// Copyright (c) 2025 By.举个🌰

Java.perform(function() {
    console.log("[*] 开始Hook Launcher性能监控...");

    // Hook Activity生命周期
    var Activity = Java.use("android.app.Activity");
    Activity.onCreate.implementation = function(savedInstanceState) {
        var className = this.getClass().getName();
        if (className.toLowerCase().includes("launcher") || className.toLowerCase().includes("home")) {
            console.log("[LAUNCHER] Activity.onCreate: " + className);
            console.log("  时间: " + new Date().toISOString());
        }
        return this.onCreate(savedInstanceState);
    };

    Activity.onResume.implementation = function() {
        var className = this.getClass().getName();
        if (className.toLowerCase().includes("launcher") || className.toLowerCase().includes("home")) {
            console.log("[LAUNCHER] Activity.onResume: " + className);
            console.log("  时间: " + new Date().toISOString());
        }
        return this.onResume();
    };

    // Hook View绘制
    var View = Java.use("android.view.View");
    View.draw.implementation = function(canvas) {
        var className = this.getClass().getName();
        if (className.toLowerCase().includes("launcher")) {
            var start_time = Date.now();
            this.draw(canvas);
            var end_time = Date.now();
            if (end_time - start_time > 16) { // 超过16ms可能影响流畅度
                console.log("[PERFORMANCE] View绘制耗时: " + (end_time - start_time) + "ms, View: " + className);
            }
            return;
        }
        return this.draw(canvas);
    };
});

// 当贝桌面性能监控 Frida Hook脚本
// Copyright (c) 2025 By.举个🌰

Java.perform(function() {
    console.log("[*] 开始Hook当贝桌面性能监控...");
    console.log("[*] 目标包名: com.dangbei.tvlauncher");
    
    // Hook Activity生命周期
    try {
        var Activity = Java.use("android.app.Activity");
        
        Activity.onCreate.implementation = function(savedInstanceState) {
            var className = this.getClass().getName();
            if (className.includes("dangbei") || className.toLowerCase().includes("launcher")) {
                console.log("[LAUNCHER] ===== Activity.onCreate =====");
                console.log("[LAUNCHER] 时间: " + new Date().toISOString());
                console.log("[LAUNCHER] 类名: " + className);
                console.log("[LAUNCHER] ========================");
            }
            return this.onCreate(savedInstanceState);
        };
        
        Activity.onStart.implementation = function() {
            var className = this.getClass().getName();
            if (className.includes("dangbei") || className.toLowerCase().includes("launcher")) {
                console.log("[LAUNCHER] ===== Activity.onStart =====");
                console.log("[LAUNCHER] 时间: " + new Date().toISOString());
                console.log("[LAUNCHER] 类名: " + className);
                console.log("[LAUNCHER] ========================");
            }
            return this.onStart();
        };
        
        Activity.onResume.implementation = function() {
            var className = this.getClass().getName();
            if (className.includes("dangbei") || className.toLowerCase().includes("launcher")) {
                console.log("[LAUNCHER] ===== Activity.onResume =====");
                console.log("[LAUNCHER] 时间: " + new Date().toISOString());
                console.log("[LAUNCHER] 类名: " + className);
                console.log("[LAUNCHER] ========================");
            }
            return this.onResume();
        };
        
        console.log("[*] Activity生命周期Hook成功");
    } catch(e) {
        console.log("[!] Activity生命周期Hook失败: " + e);
    }
    
    // Hook View绘制性能
    try {
        var View = Java.use("android.view.View");
        View.draw.implementation = function(canvas) {
            var className = this.getClass().getName();
            if (className.includes("dangbei") || className.toLowerCase().includes("launcher")) {
                var start_time = Date.now();
                this.draw(canvas);
                var end_time = Date.now();
                var draw_time = end_time - start_time;
                
                if (draw_time > 16) { // 超过16ms可能影响流畅度
                    console.log("[PERFORMANCE] ===== View绘制耗时 =====");
                    console.log("[PERFORMANCE] 时间: " + new Date().toISOString());
                    console.log("[PERFORMANCE] 耗时: " + draw_time + "ms");
                    console.log("[PERFORMANCE] View类: " + className);
                    console.log("[PERFORMANCE] ========================");
                }
                return;
            }
            return this.draw(canvas);
        };
        console.log("[*] View绘制性能Hook成功");
    } catch(e) {
        console.log("[!] View绘制性能Hook失败: " + e);
    }
    
    // Hook RecyclerView性能（如果使用）
    try {
        var RecyclerView = Java.use("androidx.recyclerview.widget.RecyclerView");
        RecyclerView.onLayout.implementation = function(changed, l, t, r, b) {
            var start_time = Date.now();
            this.onLayout(changed, l, t, r, b);
            var end_time = Date.now();
            var layout_time = end_time - start_time;
            
            if (layout_time > 10) { // 布局耗时超过10ms
                console.log("[PERFORMANCE] ===== RecyclerView布局耗时 =====");
                console.log("[PERFORMANCE] 时间: " + new Date().toISOString());
                console.log("[PERFORMANCE] 耗时: " + layout_time + "ms");
                console.log("[PERFORMANCE] 变化: " + changed);
                console.log("[PERFORMANCE] ========================");
            }
        };
        console.log("[*] RecyclerView性能Hook成功");
    } catch(e) {
        console.log("[!] RecyclerView性能Hook失败: " + e);
    }
    
    // Hook AsyncTask（如果有异步任务）
    try {
        var AsyncTask = Java.use("android.os.AsyncTask");
        AsyncTask.execute.implementation = function(params) {
            console.log("[ASYNC] ===== AsyncTask执行 =====");
            console.log("[ASYNC] 时间: " + new Date().toISOString());
            console.log("[ASYNC] 任务类: " + this.getClass().getName());
            console.log("[ASYNC] ========================");
            return this.execute(params);
        };
        console.log("[*] AsyncTask Hook成功");
    } catch(e) {
        console.log("[!] AsyncTask Hook失败: " + e);
    }
    
    console.log("[*] 当贝桌面性能监控Hook设置完成");
});

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
晶晨机顶盒按首页键性能分析工具 - 简化版
用于快速分析首页键响应问题

Copyright (c) 2025 By.举个🌰
All rights reserved.
"""

import subprocess
import time
import os
import re
from datetime import datetime


class SimpleHomeKeyAnalyzer:
    """简化版首页键性能分析器"""
    
    def __init__(self, device_ip="***************", adb_port=5555):
        self.device_ip = device_ip
        self.adb_port = adb_port
        self.device_id = f"{device_ip}:{adb_port}"
        
        print("=" * 50)
        print("晶晨机顶盒按首页键性能分析工具 - 简化版")
        print(f"设备IP: {device_ip}")
        print("版权所有 © 2025 By.举个🌰")
        print("=" * 50)

    def run_adb_command(self, command, timeout=10):
        """执行ADB命令"""
        full_command = f"adb -s {self.device_id} {command}"
        print(f"执行: {full_command}")
        
        try:
            result = subprocess.run(
                full_command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=timeout,
                encoding='utf-8',
                errors='ignore'
            )
            return result.returncode == 0, result.stdout, result.stderr
        except Exception as e:
            print(f"命令执行异常: {e}")
            return False, "", str(e)

    def check_connection(self):
        """检查ADB连接"""
        print("\n🔍 检查ADB连接...")
        
        # 尝试连接
        success, stdout, stderr = self.run_adb_command("connect")
        if not success:
            print("❌ ADB连接失败")
            return False
        
        # 测试连接
        success, stdout, stderr = self.run_adb_command("shell echo 'test'")
        if success and "test" in stdout:
            print("✅ ADB连接正常")
            return True
        else:
            print("❌ 设备无响应")
            return False

    def get_basic_info(self):
        """获取基本设备信息"""
        print("\n📱 获取设备信息...")
        
        info_commands = {
            "Android版本": "shell getprop ro.build.version.release",
            "设备型号": "shell getprop ro.product.model",
            "制造商": "shell getprop ro.product.manufacturer"
        }
        
        for name, command in info_commands.items():
            success, stdout, stderr = self.run_adb_command(command)
            if success:
                print(f"  {name}: {stdout.strip()}")
            else:
                print(f"  {name}: 获取失败")

    def capture_home_key_logs(self, duration=15):
        """捕获按首页键时的日志"""
        print(f"\n📊 开始捕获日志（{duration}秒）...")
        
        # 清除现有日志
        self.run_adb_command("logcat -c")
        
        # 创建日志文件
        log_file = f"homekey_logs_{datetime.now().strftime('%H%M%S')}.txt"
        
        print("⚠️  请在5秒后按下设备的首页键！")
        time.sleep(5)
        
        print("🔴 开始记录日志，请现在按首页键...")
        
        # 捕获日志
        logcat_command = f"adb -s {self.device_id} logcat -v time"
        
        try:
            with open(log_file, "w", encoding="utf-8") as f:
                process = subprocess.Popen(
                    logcat_command,
                    shell=True,
                    stdout=f,
                    stderr=subprocess.PIPE,
                    text=True,
                    encoding='utf-8',
                    errors='ignore'
                )
                
                time.sleep(duration)
                process.terminate()
                process.wait()
                
            print(f"✅ 日志已保存到: {log_file}")
            return log_file
            
        except Exception as e:
            print(f"❌ 日志捕获失败: {e}")
            return None

    def analyze_logs(self, log_file):
        """分析日志文件"""
        if not log_file or not os.path.exists(log_file):
            print("❌ 日志文件不存在")
            return
        
        print(f"\n🔍 分析日志文件: {log_file}")
        
        try:
            with open(log_file, "r", encoding="utf-8", errors='ignore') as f:
                content = f.read()
            
            # 查找关键事件
            home_key_events = re.findall(r'.*KEYCODE_HOME.*', content, re.IGNORECASE)
            launcher_events = re.findall(r'.*launcher.*', content, re.IGNORECASE)
            activity_events = re.findall(r'.*ActivityManager.*START.*', content, re.IGNORECASE)
            
            print(f"📈 分析结果:")
            print(f"  首页键事件: {len(home_key_events)} 个")
            print(f"  启动器事件: {len(launcher_events)} 个")
            print(f"  Activity启动事件: {len(activity_events)} 个")
            
            # 显示关键事件
            if home_key_events:
                print(f"\n🔑 首页键事件:")
                for i, event in enumerate(home_key_events[:3], 1):
                    print(f"  {i}. {event.strip()}")
            
            if launcher_events:
                print(f"\n🚀 启动器事件:")
                for i, event in enumerate(launcher_events[:3], 1):
                    print(f"  {i}. {event.strip()}")
            
            # 生成简单报告
            report_file = f"analysis_report_{datetime.now().strftime('%H%M%S')}.txt"
            with open(report_file, "w", encoding="utf-8") as f:
                f.write("晶晨机顶盒按首页键性能分析报告\n")
                f.write("=" * 40 + "\n")
                f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"设备IP: {self.device_ip}\n")
                f.write("版权所有 © 2025 By.举个🌰\n\n")
                
                f.write("事件统计:\n")
                f.write(f"  首页键事件: {len(home_key_events)} 个\n")
                f.write(f"  启动器事件: {len(launcher_events)} 个\n")
                f.write(f"  Activity启动事件: {len(activity_events)} 个\n\n")
                
                if home_key_events:
                    f.write("首页键事件详情:\n")
                    for i, event in enumerate(home_key_events, 1):
                        f.write(f"  {i}. {event.strip()}\n")
                    f.write("\n")
                
                if launcher_events:
                    f.write("启动器事件详情:\n")
                    for i, event in enumerate(launcher_events, 1):
                        f.write(f"  {i}. {event.strip()}\n")
            
            print(f"📄 分析报告已保存: {report_file}")
            
        except Exception as e:
            print(f"❌ 日志分析失败: {e}")

    def generate_frida_script(self):
        """生成基础的Frida Hook脚本"""
        print("\n🔧 生成Frida Hook脚本...")
        
        script_content = '''// 晶晨机顶盒首页键性能分析 Frida脚本
// Copyright (c) 2025 By.举个🌰

Java.perform(function() {
    console.log("[*] 开始Hook首页键处理...");
    
    // Hook InputManagerService
    try {
        var InputManagerService = Java.use("com.android.server.input.InputManagerService");
        InputManagerService.notifyKey.implementation = function(deviceId, source, policyFlags, action, flags, keyCode, scanCode, metaState, downTime, eventTime) {
            if (keyCode == 3) { // KEYCODE_HOME
                console.log("[HOME_KEY] 首页键事件 - 时间: " + new Date().toISOString());
                console.log("  动作: " + (action == 0 ? "按下" : "抬起"));
            }
            return this.notifyKey(deviceId, source, policyFlags, action, flags, keyCode, scanCode, metaState, downTime, eventTime);
        };
        console.log("[*] InputManagerService Hook成功");
    } catch(e) {
        console.log("[!] InputManagerService Hook失败: " + e);
    }
    
    // Hook ActivityManagerService
    try {
        var ActivityManagerService = Java.use("com.android.server.am.ActivityManagerService");
        ActivityManagerService.startHomeActivityLocked.implementation = function(userId, reason) {
            console.log("[HOME_ACTIVITY] 启动首页Activity - 时间: " + new Date().toISOString());
            var start_time = Date.now();
            var result = this.startHomeActivityLocked(userId, reason);
            var end_time = Date.now();
            console.log("  启动耗时: " + (end_time - start_time) + "ms");
            return result;
        };
        console.log("[*] ActivityManagerService Hook成功");
    } catch(e) {
        console.log("[!] ActivityManagerService Hook失败: " + e);
    }
});
'''
        
        script_file = "homekey_hook.js"
        with open(script_file, "w", encoding="utf-8") as f:
            f.write(script_content)
        
        print(f"✅ Frida脚本已生成: {script_file}")
        print("💡 使用方法:")
        print(f"   frida -U -l {script_file} system_server")

    def run_analysis(self):
        """运行完整分析"""
        print("\n🚀 开始完整分析流程...")
        
        # 1. 检查连接
        if not self.check_connection():
            print("❌ 无法连接设备，分析终止")
            return False
        
        # 2. 获取设备信息
        self.get_basic_info()
        
        # 3. 捕获日志
        log_file = self.capture_home_key_logs()
        
        # 4. 分析日志
        if log_file:
            self.analyze_logs(log_file)
        
        # 5. 生成Frida脚本
        self.generate_frida_script()
        
        print("\n🎉 分析完成！")
        return True


def main():
    """主函数"""
    print("请输入设备IP地址（默认: ***************）:")
    device_ip = input().strip()
    if not device_ip:
        device_ip = "***************"
    
    analyzer = SimpleHomeKeyAnalyzer(device_ip)
    
    try:
        analyzer.run_analysis()
    except KeyboardInterrupt:
        print("\n⚠️  分析被用户中断")
    except Exception as e:
        print(f"\n💥 分析异常: {e}")


if __name__ == "__main__":
    main()
